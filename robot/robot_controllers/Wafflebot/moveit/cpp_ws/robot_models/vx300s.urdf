<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from /home/<USER>/interbotix_ws/src/interbotix_ros_manipulators/interbotix_ros_xsarms/interbotix_xsarm_descriptions/urdf/vx300s.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="vx300s">
  <material name="interbotix_black">
    <texture filename="package://interbotix_xsarm_descriptions/meshes/interbotix_black.png"/>
  </material>
  <!-- use_world_frame -->
  <link name="vx300s/base_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/base.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/base.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.0534774000 -0.0005625750 0.0205961000"/>
      <mass value="0.969034"/>
      <inertia ixx="0.0060240000" ixy="0.0000471300" ixz="0.0000038510" iyy="0.0017000000" iyz="-0.0000841500" izz="0.0071620000"/>
    </inertial>
  </link>
  <joint name="waist" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="10" lower="-3.141582653589793" upper="3.141582653589793" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0 0 0.079"/>
    <parent link="vx300s/base_link"/>
    <child link="vx300s/shoulder_link"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>
  <link name="vx300s/shoulder_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 -0.003"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/shoulder.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 -0.003"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/shoulder.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0002592330 -0.0000033552 0.0116129000"/>
      <mass value="0.798614"/>
      <inertia ixx="0.0009388000" ixy="-0.0000000010" ixz="-0.0000000191" iyy="0.0011380000" iyz="0.0000059568" izz="0.0012010000"/>
    </inertial>
  </link>
  <joint name="shoulder" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="20" lower="-1.8500490071139892" upper="1.2566370614359172" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0 0 0.04805"/>
    <parent link="vx300s/shoulder_link"/>
    <child link="vx300s/upper_arm_link"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>
  <link name="vx300s/upper_arm_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/upper_arm.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/upper_arm.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0206949000 0.0000000004 0.2264590000"/>
      <mass value="0.792592"/>
      <inertia ixx="0.0089250000" ixy="0.0000000000" ixz="0.0000000000" iyy="0.0089370000" iyz="0.0012010000" izz="0.0009357000"/>
    </inertial>
  </link>
  <joint name="elbow" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="15" lower="-1.7627825445142729" upper="1.6057029118347832" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0.05955 0 0.3"/>
    <parent link="vx300s/upper_arm_link"/>
    <child link="vx300s/upper_forearm_link"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>
  <link name="vx300s/upper_forearm_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/upper_forearm.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/upper_forearm.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.105723 0 0"/>
      <mass value="0.322228"/>
      <inertia ixx="0.0001524000" ixy="-0.0000188300" ixz="-0.0000084064" iyy="0.0013420000" iyz="0.0000012564" izz="0.0014410000"/>
    </inertial>
  </link>
  <joint name="forearm_roll" type="revolute">
    <axis xyz="1 0 0"/>
    <limit effort="2" lower="-3.141582653589793" upper="3.141582653589793" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0.2 0 0"/>
    <parent link="vx300s/upper_forearm_link"/>
    <child link="vx300s/lower_forearm_link"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>
  <link name="vx300s/lower_forearm_link">
    <visual>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/lower_forearm.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/lower_forearm.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="3.141592653589793 0 0" xyz="0.0513477000 0.0068046200 0"/>
      <mass value="0.414823"/>
      <inertia ixx="0.0001753000" ixy="-0.0000852800" ixz="0" iyy="0.0005269000" iyz="0" izz="0.0005911000"/>
    </inertial>
  </link>
  <joint name="wrist_angle" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="5" lower="-1.8675022996339325" upper="2.234021442552742" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0.1 0 0"/>
    <parent link="vx300s/lower_forearm_link"/>
    <child link="vx300s/wrist_link"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>
  <link name="vx300s/wrist_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/wrist.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/wrist.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0467430000 -0.0000076652 0.0105650000"/>
      <mass value="0.115395"/>
      <inertia ixx="0.0000463100" ixy="0.0000000195" ixz="0.0000000023" iyy="0.0000451400" iyz="0.0000042002" izz="0.0000527000"/>
    </inertial>
  </link>
  <!-- Include the gripper if used -->
  <joint name="wrist_rotate" type="revolute">
    <axis xyz="1 0 0"/>
    <limit effort="1" lower="-3.141582653589793" upper="3.141582653589793" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0.069744 0 0"/>
    <parent link="vx300s/wrist_link"/>
    <child link="vx300s/gripper_link"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>
  <link name="vx300s/gripper_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.02 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/gripper.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.02 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/gripper.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0230010000 0.0000000000 0.0115230000"/>
      <mass value="0.097666"/>
      <inertia ixx="0.0000326800" ixy="0.0000000000" ixz="0.0000000000" iyy="0.0000243600" iyz="0.0000002785" izz="0.0000211900"/>
    </inertial>
  </link>
  <joint name="ee_arm" type="fixed">
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0.042825 0 0"/>
    <parent link="vx300s/gripper_link"/>
    <child link="vx300s/ee_arm_link"/>
  </joint>
  <link name="vx300s/ee_arm_link">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
  <joint name="gripper" type="continuous">
    <axis xyz="1 0 0"/>
    <limit effort="1" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0.005675 0 0"/>
    <parent link="vx300s/ee_arm_link"/>
    <child link="vx300s/gripper_prop_link"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>
  <link name="vx300s/gripper_prop_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.0685 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/gripper_prop.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.0685 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/gripper_prop.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0023780000 0.0000000285 0.0000000000"/>
      <mass value="0.008009"/>
      <inertia ixx="0.0000020386" ixy="0.0000000000" ixz="0.0000006559" iyy="0.0000042979" iyz="0.0000000000" izz="0.0000023796"/>
    </inertial>
  </link>
  <!-- If the AR tag is being used, then add the AR tag mount -->
  <!-- show_ar_tag -->
  <!-- If the gripper bar is being used, then also add the gripper bar -->
  <joint name="gripper_bar" type="fixed">
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="vx300s/ee_arm_link"/>
    <child link="vx300s/gripper_bar_link"/>
  </joint>
  <link name="vx300s/gripper_bar_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.063 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/gripper_bar.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.063 0 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/gripper_bar.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0067940000 -0.0000004272 -0.0007760000"/>
      <mass value="0.150986"/>
      <inertia ixx="0.0000789500" ixy="-0.0000000012" ixz="0.0000001341" iyy="0.0003283000" iyz="0.0000017465" izz="0.0003095000"/>
    </inertial>
  </link>
  <joint name="ee_bar" type="fixed">
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0.025875 0 0"/>
    <parent link="vx300s/gripper_bar_link"/>
    <child link="vx300s/fingers_link"/>
  </joint>
  <link name="vx300s/fingers_link">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
  <!-- If the gripper fingers are being used, add those as well -->
  <joint name="left_finger" type="prismatic">
    <axis xyz="0 1 0"/>
    <limit effort="5" lower="0.021" upper="0.057" velocity="1"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="vx300s/fingers_link"/>
    <child link="vx300s/left_finger_link"/>
    <dynamics damping="0.1" friction="0.1"/>
  </joint>
  <link name="vx300s/left_finger_link">
    <visual>
      <origin rpy="1.5707963267948966 -3.141592653589793 1.5707963267948966" xyz="-0.0404 -0.0575 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/gripper_finger.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 -3.141592653589793 1.5707963267948966" xyz="-0.0404 -0.0575 0"/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/gripper_finger.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="3.141592653589793 3.141592653589793 1.5707963267948966" xyz="0.0173440000 -0.0060692000 0.0000000000"/>
      <mass value="0.034796"/>
      <inertia ixx="0.0000243300" ixy="-0.0000024004" ixz="0.0000000000" iyy="0.0000125500" iyz="0.0000000000" izz="0.0000141700"/>
    </inertial>
  </link>
  <joint name="right_finger" type="prismatic">
    <axis xyz="0 1 0"/>
    <limit effort="5" lower="-0.057" upper="-0.021" velocity="1"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="vx300s/fingers_link"/>
    <child link="vx300s/right_finger_link"/>
    <dynamics damping="0.1" friction="0.1"/>
    <mimic joint="left_finger" multiplier="-1" offset="0"/>
  </joint>
  <link name="vx300s/right_finger_link">
    <visual>
      <origin rpy="-1.5707963267948966 3.141592653589793 -1.5707963267948966" xyz="-0.0404 0.0575 0 "/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/gripper_finger.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="interbotix_black"/>
    </visual>
    <collision>
      <origin rpy="-1.5707963267948966 3.141592653589793 -1.5707963267948966" xyz="-0.0404 0.0575 0 "/>
      <geometry>
        <mesh filename="package://interbotix_xsarm_descriptions/meshes/vx300s_meshes/gripper_finger.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 3.141592653589793 1.5707963267948966" xyz="0.0173440000 0.0060692000  0.0000000000"/>
      <mass value="0.034796"/>
      <inertia ixx="0.0000243300" ixy="0.0000024001" ixz="0.0000000000" iyy="0.0000125500" iyz="0.0000000000" izz="0.0000141700"/>
    </inertial>
  </link>
  <joint name="ee_gripper" type="fixed">
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0.0385 0 0"/>
    <parent link="vx300s/fingers_link"/>
    <child link="vx300s/ee_gripper_link"/>
  </joint>
  <link name="vx300s/ee_gripper_link">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
  <!-- show_gripper_fingers -->
  <!-- show_gripper_bar -->
  <!-- use_gripper -->
  <ros2_control name="XSHardwareInterface" type="system">
    <hardware>
      <plugin>interbotix_xs_ros_control/XSHardwareInterface</plugin>
      <param name="loop_hz">10</param>
      <param name="group_name">arm</param>
      <param name="gripper_name">gripper</param>
      <param name="joint_states_topic">joint_states</param>
    </hardware>
    <joint name="waist">
      <command_interface name="position">
        <param name="min">&quot;-3.141582653589793&quot;</param>
        <param name="max">&quot;3.141582653589793&quot;</param>
      </command_interface>
      <state_interface name="position"/>
    </joint>
    <joint name="shoulder">
      <command_interface name="position">
        <param name="min">&quot;-1.8500490071139892&quot;</param>
        <param name="max">&quot;1.2566370614359172&quot;</param>
      </command_interface>
      <state_interface name="position"/>
    </joint>
    <joint name="elbow">
      <command_interface name="position">
        <param name="min">&quot;-1.7627825445142729&quot;</param>
        <param name="max">&quot;1.6057029118347832&quot;</param>
      </command_interface>
      <state_interface name="position"/>
    </joint>
    <joint name="forearm_roll">
      <command_interface name="position">
        <param name="min">&quot;-3.141582653589793&quot;</param>
        <param name="max">&quot;3.141582653589793&quot;</param>
      </command_interface>
      <state_interface name="position"/>
    </joint>
    <joint name="wrist_angle">
      <command_interface name="position">
        <param name="min">&quot;-1.8675022996339325&quot;</param>
        <param name="max">&quot;2.234021442552742&quot;</param>
      </command_interface>
      <state_interface name="position"/>
    </joint>
    <joint name="wrist_rotate">
      <command_interface name="position">
        <param name="min">&quot;-3.141582653589793&quot;</param>
        <param name="max">&quot;3.141582653589793&quot;</param>
      </command_interface>
      <state_interface name="position"/>
    </joint>
    <joint name="left_finger">
      <command_interface name="position">
        <param name="min">&quot;0.021&quot;</param>
        <param name="max">&quot;-0.021&quot;</param>
      </command_interface>
      <state_interface name="position"/>
    </joint>
  </ros2_control>
</robot>
