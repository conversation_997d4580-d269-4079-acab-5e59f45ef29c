<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from /home/<USER>/interbotix_ws/src/interbotix_ros_manipulators/interbotix_ros_xsarms/interbotix_xsarm_moveit/config/srdf/vx300s.srdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<!--This does not replace URDF, and is not an extension of URDF.
    This is a format for representing semantic information about the robot structure.
    A URDF file must exist for this robot as well, where the joints and the links that are referenced are defined
-->
<robot name="vx300s">
  <group name="interbotix_arm">
    <joint name="waist"/>
    <joint name="shoulder"/>
    <joint name="elbow"/>
    <joint name="forearm_roll"/>
    <joint name="wrist_angle"/>
    <joint name="wrist_rotate"/>
    <joint name="ee_arm"/>
    <joint name="gripper_bar"/>
    <joint name="ee_bar"/>
    <joint name="ee_gripper"/>
  </group>
  <group name="interbotix_gripper">
    <link name="vx300s/left_finger_link"/>
    <link name="vx300s/right_finger_link"/>
  </group>
  <!--GROUP STATES - Purpose - Define a named state for a particular group, in terms of joint values. This is useful to define states like 'folded arms'-->
  <group_state group="interbotix_arm" name="Home">
    <joint name="elbow" value="0"/>
    <joint name="forearm_roll" value="0"/>
    <joint name="shoulder" value="0"/>
    <joint name="waist" value="0"/>
    <joint name="wrist_angle" value="0"/>
    <joint name="wrist_rotate" value="0"/>
  </group_state>
  <group_state group="interbotix_arm" name="Upright">
    <joint name="elbow" value="-1.5708"/>
    <joint name="forearm_roll" value="0"/>
    <joint name="shoulder" value="0"/>
    <joint name="waist" value="0"/>
    <joint name="wrist_angle" value="0"/>
    <joint name="wrist_rotate" value="0"/>
  </group_state>
  <group_state group="interbotix_arm" name="Sleep">
    <joint name="elbow" value="1.55"/>
    <joint name="forearm_roll" value="0"/>
    <joint name="shoulder" value="-1.76"/>
    <joint name="waist" value="0"/>
    <joint name="wrist_angle" value="0.8"/>
    <joint name="wrist_rotate" value="0"/>
  </group_state>
  <group_state group="interbotix_gripper" name="Grasping">
    <joint name="left_finger" value="0.021"/>
    <joint name="right_finger" value="-0.021"/>
  </group_state>
  <group_state group="interbotix_gripper" name="Released">
    <joint name="left_finger" value="0.057"/>
    <joint name="right_finger" value="-0.057"/>
  </group_state>
  <group_state group="interbotix_gripper" name="Home">
    <joint name="left_finger" value="0.03"/>
    <joint name="right_finger" value="-0.03"/>
  </group_state>
  <!--END EFFECTOR - Purpose - Represent information about an end effector.-->
  <end_effector group="interbotix_gripper" name="interbotix_gripper" parent_link="vx300s/ee_gripper_link"/>
  <!--DISABLE COLLISIONS - By default it is assumed that any link of the robot could potentially come into collision with any other link in the robot. This tag disables collision checking between a specified pair of links. -->
  <disable_collisions link1="vx300s/base_link" link2="vx300s/shoulder_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/base_link" link2="vx300s/upper_arm_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_bar_link" link2="vx300s/gripper_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/gripper_bar_link" link2="vx300s/gripper_prop_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/gripper_bar_link" link2="vx300s/left_finger_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/gripper_bar_link" link2="vx300s/lower_forearm_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_bar_link" link2="vx300s/right_finger_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/gripper_bar_link" link2="vx300s/upper_forearm_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_bar_link" link2="vx300s/wrist_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_link" link2="vx300s/gripper_prop_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/gripper_link" link2="vx300s/left_finger_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_link" link2="vx300s/lower_forearm_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_link" link2="vx300s/right_finger_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_link" link2="vx300s/upper_forearm_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_link" link2="vx300s/wrist_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/gripper_prop_link" link2="vx300s/left_finger_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_prop_link" link2="vx300s/lower_forearm_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_prop_link" link2="vx300s/right_finger_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_prop_link" link2="vx300s/upper_forearm_link" reason="Never"/>
  <disable_collisions link1="vx300s/gripper_prop_link" link2="vx300s/wrist_link" reason="Never"/>
  <disable_collisions link1="vx300s/left_finger_link" link2="vx300s/lower_forearm_link" reason="Never"/>
  <disable_collisions link1="vx300s/left_finger_link" link2="vx300s/right_finger_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/left_finger_link" link2="vx300s/upper_forearm_link" reason="Never"/>
  <disable_collisions link1="vx300s/left_finger_link" link2="vx300s/wrist_link" reason="Never"/>
  <disable_collisions link1="vx300s/lower_forearm_link" link2="vx300s/right_finger_link" reason="Never"/>
  <disable_collisions link1="vx300s/lower_forearm_link" link2="vx300s/upper_forearm_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/lower_forearm_link" link2="vx300s/wrist_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/right_finger_link" link2="vx300s/upper_forearm_link" reason="Never"/>
  <disable_collisions link1="vx300s/right_finger_link" link2="vx300s/wrist_link" reason="Never"/>
  <disable_collisions link1="vx300s/shoulder_link" link2="vx300s/upper_arm_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/shoulder_link" link2="vx300s/upper_forearm_link" reason="Never"/>
  <disable_collisions link1="vx300s/upper_arm_link" link2="vx300s/upper_forearm_link" reason="Adjacent"/>
  <disable_collisions link1="vx300s/upper_forearm_link" link2="vx300s/wrist_link" reason="Never"/>
</robot>
