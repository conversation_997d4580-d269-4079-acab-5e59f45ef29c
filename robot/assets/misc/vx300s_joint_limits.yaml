# joint_limits.yaml allows the dynamics properties specified in the URDF to be overwritten or augmented as needed
# Specific joint properties can be changed with the keys [max_position, min_position, max_velocity, max_acceleration]
# Joint limits can be turned off with [has_velocity_limits, has_acceleration_limits]

default_velocity_scaling_factor: 1.0
default_acceleration_scaling_factor: 1.0

joint_limits:
  waist:
    has_velocity_limits: true
    max_velocity: 15.5
    has_acceleration_limits: true
    max_acceleration: 25.5
  shoulder:
    min_position: -0.785398
    has_velocity_limits: true
    max_velocity: 15.5
    has_acceleration_limits: true
    max_acceleration: 25.5
  elbow:
    has_velocity_limits: true
    max_velocity: 15.5
    has_acceleration_limits: true
    max_acceleration: 25.5
  forearm_roll:
    max_position: 1.55
    min_position: -1.55
    has_velocity_limits: true
    max_velocity: 15.5
    has_acceleration_limits: true
    max_acceleration: 25.5
  wrist_angle:
    has_velocity_limits: true
    max_velocity: 15.5
    has_acceleration_limits: true
    max_acceleration: 25.5
  wrist_rotate:
    max_position: 1.55
    min_position: -1.55
    has_velocity_limits: true
    max_velocity: 15.5
    has_acceleration_limits: true
    max_acceleration: 25.5
  left_finger:
    has_velocity_limits: true
    max_velocity: 1.0
    has_acceleration_limits: true
    max_acceleration: 0.01
  right_finger:
    has_velocity_limits: true
    max_velocity: 1.0
    max_acceleration: 0.01
