<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by Microsoft Visio, SVG Export layot.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="11.6929in" height="8.26772in"
		viewBox="0 0 841.89 595.276" xml:space="preserve" color-interpolation-filters="sRGB" class="st8">
	<v:documentProperties v:langID="1044" v:metric="true" v:viewMarkup="false">
		<v:userDefs>
			<v:ud v:nameU="msvNoAutoConnect" v:val="VT0(0):26"/>
		</v:userDefs>
	</v:documentProperties>

	<style type="text/css">
	<![CDATA[
		.st1 {fill:#ffffff;stroke:#c8c8c8;stroke-width:0.25}
		.st2 {fill:#595959;stroke:#c8c8c8;stroke-width:0.25}
		.st3 {fill:#bf4f14;stroke:#c8c8c8;stroke-width:0.25}
		.st4 {fill:#45b0e1;stroke:#c8c8c8;stroke-width:0.25}
		.st5 {fill:#ffffff;font-family:Calibri;font-size:2.50001em}
		.st6 {fill:#8ed873;stroke:#c8c8c8;stroke-width:0.25}
		.st7 {fill:none;stroke:none;stroke-width:0.25}
		.st8 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<v:userDefs>
			<v:ud v:nameU="msvThemeOrder" v:val="VT0(0):26"/>
		</v:userDefs>
		<title>Side-1</title>
		<v:pageProperties v:drawingScale="0.0393701" v:pageScale="0.0393701" v:drawingUnits="24" v:shadowOffsetX="8.50394"
				v:shadowOffsetY="-8.50394"/>
		<v:layer v:name="Flowchart" v:index="0"/>
		<v:layer v:name="Connector" v:index="1"/>
		<g id="shape1-1" v:mID="1" v:groupContext="shape" v:layerMember="0" transform="translate(212.598,-191.339)">
			<title>Process</title>
			<v:custProps>
				<v:cp v:nameU="Cost" v:lbl="Cost" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="ProcessNumber" v:lbl="Process Number" v:prompt="" v:type="2" v:format="" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Owner" v:lbl="Owner" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Function" v:lbl="Function" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0" v:val="VT4()"/>
				<v:cp v:nameU="StartDate" v:lbl="Start Date" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="EndDate" v:lbl="End Date" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Status" v:lbl="Status" v:prompt="" v:type="4"
						v:format=";Not Started;In Progress;Completed;Deferred;Waiting on Input" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0" v:val="VT4()"/>
			</v:custProps>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
				<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
			</v:userDefs>
			<rect x="0" y="340.157" width="425.197" height="255.118" class="st1"/>
		</g>
		<g id="shape2-3" v:mID="2" v:groupContext="shape" v:layerMember="0" transform="translate(212.598,-318.898)">
			<title>Process.2</title>
			<v:custProps>
				<v:cp v:nameU="Cost" v:lbl="Cost" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="ProcessNumber" v:lbl="Process Number" v:prompt="" v:type="2" v:format="" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Owner" v:lbl="Owner" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Function" v:lbl="Function" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0" v:val="VT4()"/>
				<v:cp v:nameU="StartDate" v:lbl="Start Date" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="EndDate" v:lbl="End Date" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Status" v:lbl="Status" v:prompt="" v:type="4"
						v:format=";Not Started;In Progress;Completed;Deferred;Waiting on Input" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0" v:val="VT4()"/>
			</v:custProps>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
				<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
			</v:userDefs>
			<rect x="0" y="467.717" width="425.197" height="127.559" class="st2"/>
		</g>
		<g id="shape3-5" v:mID="3" v:groupContext="shape" v:layerMember="0" transform="translate(212.598,-191.339)">
			<title>Process.3</title>
			<v:custProps>
				<v:cp v:nameU="Cost" v:lbl="Cost" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="ProcessNumber" v:lbl="Process Number" v:prompt="" v:type="2" v:format="" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Owner" v:lbl="Owner" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Function" v:lbl="Function" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0" v:val="VT4()"/>
				<v:cp v:nameU="StartDate" v:lbl="Start Date" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="EndDate" v:lbl="End Date" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Status" v:lbl="Status" v:prompt="" v:type="4"
						v:format=";Not Started;In Progress;Completed;Deferred;Waiting on Input" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0" v:val="VT4()"/>
			</v:custProps>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
				<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
			</v:userDefs>
			<rect x="0" y="467.717" width="425.197" height="127.559" class="st3"/>
		</g>
		<g id="shape4-7" v:mID="4" v:groupContext="shape" v:layerMember="0" transform="translate(212.598,-318.898)">
			<title>Process.4</title>
			<desc>Waffle iron</desc>
			<v:custProps>
				<v:cp v:nameU="Cost" v:lbl="Cost" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="ProcessNumber" v:lbl="Process Number" v:prompt="" v:type="2" v:format="" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Owner" v:lbl="Owner" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Function" v:lbl="Function" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0" v:val="VT4()"/>
				<v:cp v:nameU="StartDate" v:lbl="Start Date" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="EndDate" v:lbl="End Date" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Status" v:lbl="Status" v:prompt="" v:type="4"
						v:format=";Not Started;In Progress;Completed;Deferred;Waiting on Input" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0" v:val="VT4()"/>
			</v:custProps>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
				<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="106.299" cy="531.496" width="212.6" height="127.559"/>
			<rect x="0" y="467.717" width="212.598" height="127.559" class="st4"/>
			<text x="37.85" y="540.5" class="st5" v:langID="1044"><v:paragraph v:horizAlign="1"/><v:tabList/>Waffle iron</text>		</g>
		<g id="shape5-10" v:mID="5" v:groupContext="shape" v:layerMember="0" transform="translate(212.598,-191.339)">
			<title>Process.5</title>
			<desc>Batter</desc>
			<v:custProps>
				<v:cp v:nameU="Cost" v:lbl="Cost" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="ProcessNumber" v:lbl="Process Number" v:prompt="" v:type="2" v:format="" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Owner" v:lbl="Owner" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Function" v:lbl="Function" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0" v:val="VT4()"/>
				<v:cp v:nameU="StartDate" v:lbl="Start Date" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="EndDate" v:lbl="End Date" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0"/>
				<v:cp v:nameU="Status" v:lbl="Status" v:prompt="" v:type="4"
						v:format=";Not Started;In Progress;Completed;Deferred;Waiting on Input" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1033" v:cal="0" v:val="VT4()"/>
			</v:custProps>
			<v:userDefs>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
				<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="106.299" cy="531.496" width="212.6" height="127.559"/>
			<rect x="0" y="467.717" width="212.598" height="127.559" class="st6"/>
			<text x="68.21" y="540.5" class="st5" v:langID="1044"><v:paragraph v:horizAlign="1"/><v:tabList/>Batter</text>		</g>
		<g id="shape9-13" v:mID="9" v:groupContext="shape" transform="translate(425.197,-318.898)">
			<title>Sheet.9</title>
			<desc>Robot arm</desc>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="106.299" cy="531.496" width="212.6" height="127.559"/>
			<rect x="0" y="467.717" width="212.598" height="127.559" class="st7"/>
			<text x="41.64" y="540.5" class="st5" v:langID="1044"><v:paragraph v:horizAlign="1"/><v:tabList/>Robot arm</text>		</g>
		<g id="shape12-16" v:mID="12" v:groupContext="shape" transform="translate(425.197,-191.339)">
			<title>Sheet.12</title>
			<desc>Waffle delivery</desc>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="106.299" cy="531.496" width="212.6" height="127.559"/>
			<rect x="0" y="467.717" width="212.598" height="127.559" class="st7"/>
			<text x="13.83" y="540.5" class="st5" v:langID="1044"><v:paragraph v:horizAlign="1"/><v:tabList/>Waffle delivery</text>		</g>
	</g>
</svg>
