"""
Waffle Making Robot Main Control Script

This script integrates the robot control system with a touchscreen GUI interface.
It uses an object-oriented approach to manage the robot and GUI components.
"""

import threading
import queue
import customtkinter as ctk
from robot.tools.errorhandling import handle_error
from screen.sidebar import Sidebar
from screen.pagecontroller import <PERSON><PERSON><PERSON>roller
from main.robot_controller import Robot
from main.page_connector import Screen
from rclpy._rclpy_pybind11 import RCLError
import signal


# Global appearance for the GUI
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

# Font constants
FONT_TITLE = ("Arial", 48)
FONT_BUTTON = ("Arial", 36)
FONT_PIN_ENTRY = ("Arial", 48)


class DelayedKeyboardInterrupt:

    def __enter__(self):
        self.signal_received = False
        self.old_handler = signal.signal(signal.SIGINT, self.handler)
                
    def handler(self, sig, frame):
        self.signal_received = (sig, frame)
        logging.debug('SIGINT received. Delaying KeyboardInterrupt.')
    
    def __exit__(self, type, value, traceback):
        signal.signal(signal.SIGINT, self.old_handler)
        if self.signal_received:
            self.old_handler(*self.signal_received)

class WaffleApp(ctk.CTk):
    """Main application class that integrates the touchscreen GUI with the robot control system."""

    def __init__(self, command_queue, state_queue, status_queue, stop_event):
        """Initialize the application.

        Args:
            command_queue: Queue for sending commands to the robot controller
            state_queue: Queue for receiving state updates from the robot controller
            status_queue: Queue for receiving status messages from the robot controller
            stop_event: Event for signaling the application to stop
        """
        super().__init__()
        self.title("Waffle Making Robot")
        self.attributes("-fullscreen", True)

        # Store communication queues
        self.command_queue = command_queue
        self.state_queue = state_queue
        self.status_queue = status_queue
        self.stop_event = stop_event

        # Configure the grid layout
        self.grid_columnconfigure(0, minsize=300)
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Create the sidebar and page controller
        self.sidebar = Sidebar(self, button_callback=self.on_button_click)
        self.sidebar.grid(row=0, column=0, sticky="nsew")
        self.page_controller = PageController(self)
        self.page_controller.grid(row=0, column=1, sticky="nsew")

        # Initialize status display
        self.status_var = ctk.StringVar(value="Initializing...")
        self.status_label = ctk.CTkLabel(
            self,
            textvariable=self.status_var,
            font=("Arial", 16),
            fg_color="#333333",
            corner_radius=8
        )
        self.status_label.place(relx=0.5, rely=0.95, anchor="center", relwidth=0.9, relheight=0.05)

        # Start the status update thread
        self.status_update_thread = threading.Thread(target=self.update_status, daemon=True)
        self.status_update_thread.start()

    def on_button_click(self, button_name):
        """Handle sidebar button clicks."""
        if button_name == "Back":
            self.page_controller.go_back()
        elif button_name == "Emergency":
            # Put emergency stop command in the queue
            self.command_queue.put(("EMERGENCY_STOP", None))
            self.page_controller.show_page("Emergency")
        else:
            self.page_controller.show_page(button_name)

    def update_status(self):
        """Update the status display from the status queue."""
        while not self.stop_event.is_set():
            try:
                status = self.status_queue.get(timeout=0.1)
                self.status_var.set(status)
            except queue.Empty:
                pass

    def send_command(self, command, data=None):
        """Send a command to the robot control thread."""
        print(f"DEBUG: Sending command to robot: {command}, data: {data}")
        self.command_queue.put((command, data))
        print(f"DEBUG: Command {command} added to queue")

    def exit_application(self):
        """Clean exit of the application."""
        self.stop_event.set()
        self.quit()


def main():
    """Simple main function - just start the waffle making system."""
    # Create and start the waffle system
    waffle_system = WaffleSystem()
    waffle_system.start()
    waffle_system.run()


class WaffleSystem:
    """Simple wrapper that manages the entire waffle making system."""

    def __init__(self):
        """Initialize the waffle system."""
        # Create communication
        self.command_queue = queue.Queue()
        self.state_queue = queue.Queue()
        self.status_queue = queue.Queue()
        self.stop_event = threading.Event()

        # Components
        self.robot_controller = None
        self.app = None
        self.page_connector = None

    def start(self):
        """Start all system components."""
        # Create robot controller
        self.robot_controller = Robot(
            self.command_queue,
            self.state_queue,
            self.status_queue,
            self.stop_event
        )
        self.robot_controller.start_robot_thread()

        # Create GUI
        self.app = WaffleApp(
            self.command_queue,
            self.state_queue,
            self.status_queue,
            self.stop_event
        )

        # Connect pages
        self.page_connector = Screen(
            self.app,
            self.app.page_controller,
            self.app.send_command,
            self.state_queue,
            self.status_queue
        )
        self.page_connector.start_stats_updates(self.robot_controller)

    def run(self):
        """Run the main GUI loop."""
        try:
            self.app.mainloop()
        finally:
            self.stop()

    def stop(self):
        """Stop all system components."""
        self.stop_event.set()
        if self.robot_controller:
            with DelayedKeyboardInterrupt():
                self.robot_controller.stop_robot_thread()

if __name__ == "__main__":
    try:
        main()
    except (Exception, KeyboardInterrupt, RCLError) as e:
        handle_error(e)
