#!/usr/bin/env python3
"""
Test script to verify camera feed functionality without robot hardware.
"""

import threading
import queue
import time
from camera.vision import Vision

def test_camera_feed():
    """Test the camera feed functionality."""
    print("Testing camera feed functionality...")
    
    try:
        # Initialize vision system
        print("Initializing vision system...")
        vision = Vision()
        print("Vision system initialized successfully!")
        
        # Test camera feed in a separate thread
        def camera_feed_thread():
            try:
                print("Starting camera feed. Press ESC in the camera window to close.")
                # Run the vision system with camera feed display
                vision.run(show_image=True, draw_cubes=True, detect_hands=False, detect_gestures=False)
                print("Camera feed closed.")
            except Exception as e:
                print(f"ERROR in camera feed: {str(e)}")
                import traceback
                print(traceback.format_exc())
        
        # Start the camera feed thread
        camera_thread = threading.Thread(target=camera_feed_thread, daemon=True)
        camera_thread.start()
        
        # Wait for the thread to complete or timeout after 30 seconds
        camera_thread.join(timeout=30)
        
        if camera_thread.is_alive():
            print("Camera feed thread is still running (this is normal if cameras are connected)")
        else:
            print("Camera feed thread completed")
            
    except Exception as e:
        print(f"Error testing camera feed: {str(e)}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    test_camera_feed()
