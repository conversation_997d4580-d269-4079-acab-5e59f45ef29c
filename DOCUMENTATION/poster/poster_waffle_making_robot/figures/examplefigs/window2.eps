%!PS-Adobe-3.0 EPSF-3.0
%%Creator: (MATLAB, The Mathworks, Inc. Version 9.9.0.1570001 \(R2020b\) Update 4. Operating System: Windows 10)
%%Title: (C:/Users/<USER>/OneDrive - Universitetet i Agder/Jobb/UiA/Fag/EVU/MATLAB/window2.eps)
%%CreationDate: 2021-02-08T19:27:03
%%Pages: (atend)
%%BoundingBox:     0     0   206   146
%%LanguageLevel: 3
%%EndComments
%%BeginProlog
%%BeginResource: procset (Apache XML Graphics Std ProcSet) 1.2 0
%%Version: 1.2 0
%%Copyright: (Copyright 2001-2003,2010 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/bd{bind def}bind def
/ld{load def}bd
/GR/grestore ld
/GS/gsave ld
/RM/rmoveto ld
/C/curveto ld
/t/show ld
/L/lineto ld
/ML/setmiterlimit ld
/CT/concat ld
/f/fill ld
/N/newpath ld
/S/stroke ld
/CC/setcmykcolor ld
/A/ashow ld
/cp/closepath ld
/RC/setrgbcolor ld
/LJ/setlinejoin ld
/GC/setgray ld
/LW/setlinewidth ld
/M/moveto ld
/re {4 2 roll M
1 index 0 rlineto
0 exch rlineto
neg 0 rlineto
cp } bd
/_ctm matrix def
/_tm matrix def
/BT { _ctm currentmatrix pop matrix _tm copy pop 0 0 moveto } bd
/ET { _ctm setmatrix } bd
/iTm { _ctm setmatrix _tm concat } bd
/Tm { _tm astore pop iTm 0 0 moveto } bd
/ux 0.0 def
/uy 0.0 def
/F {
  /Tp exch def
  /Tf exch def
  Tf findfont Tp scalefont setfont
  /cf Tf def  /cs Tp def
} bd
/ULS {currentpoint /uy exch def /ux exch def} bd
/ULE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add moveto  Tcx uy To add lineto
  Tt setlinewidth stroke
  grestore
} bd
/OLE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs add moveto Tcx uy To add cs add lineto
  Tt setlinewidth stroke
  grestore
} bd
/SOE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs 10 mul 26 idiv add moveto Tcx uy To add cs 10 mul 26 idiv add lineto
  Tt setlinewidth stroke
  grestore
} bd
/QT {
/Y22 exch store
/X22 exch store
/Y21 exch store
/X21 exch store
currentpoint
/Y21 load 2 mul add 3 div exch
/X21 load 2 mul add 3 div exch
/X21 load 2 mul /X22 load add 3 div
/Y21 load 2 mul /Y22 load add 3 div
/X22 load /Y22 load curveto
} bd
/SSPD {
dup length /d exch dict def
{
/v exch def
/k exch def
currentpagedevice k known {
/cpdv currentpagedevice k get def
v cpdv ne {
/upd false def
/nullv v type /nulltype eq def
/nullcpdv cpdv type /nulltype eq def
nullv nullcpdv or
{
/upd true def
} {
/sametype v type cpdv type eq def
sametype {
v type /arraytype eq {
/vlen v length def
/cpdvlen cpdv length def
vlen cpdvlen eq {
0 1 vlen 1 sub {
/i exch def
/obj v i get def
/cpdobj cpdv i get def
obj cpdobj ne {
/upd true def
exit
} if
} for
} {
/upd true def
} ifelse
} {
v type /dicttype eq {
v {
/dv exch def
/dk exch def
/cpddv cpdv dk get def
dv cpddv ne {
/upd true def
exit
} if
} forall
} {
/upd true def
} ifelse
} ifelse
} if
} ifelse
upd true eq {
d k v put
} if
} if
} if
} forall
d length 0 gt {
d setpagedevice
} if
} bd
/RE { % /NewFontName [NewEncodingArray] /FontName RE -
  findfont dup length dict begin
  {
    1 index /FID ne
    {def} {pop pop} ifelse
  } forall
  /Encoding exch def
  /FontName 1 index def
  currentdict definefont pop
  end
} bind def
%%EndResource
%%BeginResource: procset (Apache XML Graphics EPS ProcSet) 1.0 0
%%Version: 1.0 0
%%Copyright: (Copyright 2002-2003 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/BeginEPSF { %def
/b4_Inc_state save def         % Save state for cleanup
/dict_count countdictstack def % Count objects on dict stack
/op_count count 1 sub def      % Count objects on operand stack
userdict begin                 % Push userdict on dict stack
/showpage { } def              % Redefine showpage, { } = null proc
0 setgray 0 setlinecap         % Prepare graphics state
1 setlinewidth 0 setlinejoin
10 setmiterlimit [ ] 0 setdash newpath
/languagelevel where           % If level not equal to 1 then
{pop languagelevel             % set strokeadjust and
1 ne                           % overprint to their defaults.
{false setstrokeadjust false setoverprint
} if
} if
} bd
/EndEPSF { %def
count op_count sub {pop} repeat            % Clean up stacks
countdictstack dict_count sub {end} repeat
b4_Inc_state restore
} bd
%%EndResource
%FOPBeginFontDict
%%IncludeResource: font Courier-Oblique
%%IncludeResource: font Courier-BoldOblique
%%IncludeResource: font Courier-Bold
%%IncludeResource: font ZapfDingbats
%%IncludeResource: font Symbol
%%IncludeResource: font Helvetica
%%IncludeResource: font Helvetica-Oblique
%%IncludeResource: font Helvetica-Bold
%%IncludeResource: font Helvetica-BoldOblique
%%IncludeResource: font Times-Roman
%%IncludeResource: font Times-Italic
%%IncludeResource: font Times-Bold
%%IncludeResource: font Times-BoldItalic
%%IncludeResource: font Courier
%FOPEndFontDict
%%BeginResource: encoding WinAnsiEncoding
/WinAnsiEncoding [
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /space /exclam /quotedbl
/numbersign /dollar /percent /ampersand /quotesingle
/parenleft /parenright /asterisk /plus /comma
/hyphen /period /slash /zero /one
/two /three /four /five /six
/seven /eight /nine /colon /semicolon
/less /equal /greater /question /at
/A /B /C /D /E
/F /G /H /I /J
/K /L /M /N /O
/P /Q /R /S /T
/U /V /W /X /Y
/Z /bracketleft /backslash /bracketright /asciicircum
/underscore /quoteleft /a /b /c
/d /e /f /g /h
/i /j /k /l /m
/n /o /p /q /r
/s /t /u /v /w
/x /y /z /braceleft /bar
/braceright /asciitilde /bullet /Euro /bullet
/quotesinglbase /florin /quotedblbase /ellipsis /dagger
/daggerdbl /circumflex /perthousand /Scaron /guilsinglleft
/OE /bullet /Zcaron /bullet /bullet
/quoteleft /quoteright /quotedblleft /quotedblright /bullet
/endash /emdash /asciitilde /trademark /scaron
/guilsinglright /oe /bullet /zcaron /Ydieresis
/space /exclamdown /cent /sterling /currency
/yen /brokenbar /section /dieresis /copyright
/ordfeminine /guillemotleft /logicalnot /sfthyphen /registered
/macron /degree /plusminus /twosuperior /threesuperior
/acute /mu /paragraph /middot /cedilla
/onesuperior /ordmasculine /guillemotright /onequarter /onehalf
/threequarters /questiondown /Agrave /Aacute /Acircumflex
/Atilde /Adieresis /Aring /AE /Ccedilla
/Egrave /Eacute /Ecircumflex /Edieresis /Igrave
/Iacute /Icircumflex /Idieresis /Eth /Ntilde
/Ograve /Oacute /Ocircumflex /Otilde /Odieresis
/multiply /Oslash /Ugrave /Uacute /Ucircumflex
/Udieresis /Yacute /Thorn /germandbls /agrave
/aacute /acircumflex /atilde /adieresis /aring
/ae /ccedilla /egrave /eacute /ecircumflex
/edieresis /igrave /iacute /icircumflex /idieresis
/eth /ntilde /ograve /oacute /ocircumflex
/otilde /odieresis /divide /oslash /ugrave
/uacute /ucircumflex /udieresis /yacute /thorn
/ydieresis
] def
%%EndResource
%FOPBeginFontReencode
/Courier-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Oblique exch definefont pop
/Courier-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-BoldOblique exch definefont pop
/Courier-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Bold exch definefont pop
/Helvetica findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica exch definefont pop
/Helvetica-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Oblique exch definefont pop
/Helvetica-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Bold exch definefont pop
/Helvetica-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-BoldOblique exch definefont pop
/Times-Roman findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Roman exch definefont pop
/Times-Italic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Italic exch definefont pop
/Times-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Bold exch definefont pop
/Times-BoldItalic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-BoldItalic exch definefont pop
/Courier findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier exch definefont pop
%FOPEndFontReencode
%%EndProlog
%%Page: 1 1
%%PageBoundingBox: 0 0 206 146
%%BeginPageSetup
[1 0 0 -1 0 146] CT
%%EndPageSetup
GS
[0.60175 0 0 0.60248 0 0.2] CT
N
0 0 M
343 0 L
343 242 L
0 242 L
0 0 L
cp
clip
1 GC
N
0 0 343 242 re
f
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
1 GC
N
0 0 343 242 re
f
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
1 GC
N
59 197 M
311 197 L
311 18 L
59 18 L
cp
f
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
59 197 M
311 197 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
59 18 M
311 18 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
59 197 M
59 194.48 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
122 197 M
122 194.48 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
185 197 M
185 194.48 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
248 197 M
248 194.48 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
311 197 M
311 194.48 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
59 18 M
59 20.52 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
122 18 M
122 20.52 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
185 18 M
185 20.52 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
248 18 M
248 20.52 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
311 18 M
311 20.52 L
S
GR
GS
[0.60175 0 0 0.60248 35.50321 121.69999] CT
0.149 GC
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
-4.5 16 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.60175 0 0 0.60248 73.41341 121.69999] CT
0.149 GC
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
-10.5 16 moveto 
1 -1 scale
(0.5) t 
GR
GR
GS
[0.60175 0 0 0.60248 111.32361 121.69999] CT
0.149 GC
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
-4.5 16 moveto 
1 -1 scale
(1) t 
GR
GR
GS
[0.60175 0 0 0.60248 149.23381 121.69999] CT
0.149 GC
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
-10.5 16 moveto 
1 -1 scale
(1.5) t 
GR
GR
GS
[0.60175 0 0 0.60248 187.14402 121.69999] CT
0.149 GC
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
-4.5 16 moveto 
1 -1 scale
(2) t 
GR
GR
GS
[0.60175 0 0 0.60248 111.32369 133.95041] CT
0.149 GC
/Helvetica 16.5 F
GS
[1 0 0 1 0 0] CT
-24.5 17 moveto 
1 -1 scale
(x-akse) t 
GR
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
59 197 M
59 18 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
311 197 M
311 18 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
59 197 M
61.52 197 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
59 137.333 M
61.52 137.333 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
59 77.667 M
61.52 77.667 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
59 18 M
61.52 18 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
311 197 M
308.48 197 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
311 137.333 M
308.48 137.333 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
311 77.667 M
308.48 77.667 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
311 18 M
308.48 18 L
S
GR
GS
[0.60175 0 0 0.60248 32.69504 118.88843] CT
0.149 GC
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
-30 6 moveto 
1 -1 scale
(0.05) t 
GR
GR
GS
[0.60175 0 0 0.60248 32.69504 82.94049] CT
0.149 GC
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
-21 6 moveto 
1 -1 scale
(0.1) t 
GR
GR
GS
[0.60175 0 0 0.60248 32.69504 46.99255] CT
0.149 GC
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
-30 6 moveto 
1 -1 scale
(0.15) t 
GR
GR
GS
[0.60175 0 0 0.60248 32.69504 11.04462] CT
0.149 GC
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
-21 6 moveto 
1 -1 scale
(0.2) t 
GR
GR
GS
[0 -0.60248 0.60175 0 12.63674 64.96647] CT
0.149 GC
/Helvetica 16.5 F
GS
[1 0 0 1 0 0] CT
-24.5 -4 moveto 
1 -1 scale
(y-akse) t 
GR
GR
GS
[0.60175 0 0 0.60248 35.50321 97.31967] CT
0 0.447 0.741 RC
N
0 -4.583 M
2.531 -4.583 4.583 -2.531 4.583 0 C
4.583 0 L
4.583 2.531 2.531 4.583 0 4.583 C
-2.531 4.583 -4.583 2.531 -4.583 0 C
-4.583 -2.531 -2.531 -4.583 0 -4.583 C
cp
0 -5.417 M
-2.992 -5.417 -5.417 -2.992 -5.417 0 C
-5.417 2.992 -2.992 5.417 0 5.417 C
2.992 5.417 5.417 2.992 5.417 0 C
5.417 0 L
5.417 -2.992 2.992 -5.417 0 -5.417 C
cp
f
GR
GS
[0.60175 0 0 0.60248 111.32361 68.56132] CT
0 0.447 0.741 RC
N
/f1395082454{0 -4.583 M
2.531 -4.583 4.583 -2.531 4.583 0 C
4.583 0 L
4.583 2.531 2.531 4.583 0 4.583 C
-2.531 4.583 -4.583 2.531 -4.583 0 C
-4.583 -2.531 -2.531 -4.583 0 -4.583 C
cp
0 -5.417 M
-2.992 -5.417 -5.417 -2.992 -5.417 0 C
-5.417 2.992 -2.992 5.417 0 5.417 C
2.992 5.417 5.417 2.992 5.417 0 C
5.417 0 L
5.417 -2.992 2.992 -5.417 0 -5.417 C
cp}def
f1395082454
f
GR
GS
[0.60175 0 0 0.60248 187.14402 11.04462] CT
0 0.447 0.741 RC
N
f1395082454
f
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
1 0 0 RC
[10 6] 0 setdash
2 LJ
0.833 LW
N
59 169.156 M
71.6 161.996 L
84.2 154.836 L
96.8 147.676 L
109.4 140.516 L
122 133.356 L
134.6 126.196 L
147.2 119.036 L
159.8 111.876 L
172.4 104.716 L
185 97.556 L
197.6 90.396 L
210.2 83.236 L
222.8 76.076 L
235.4 68.916 L
248 61.756 L
260.6 54.596 L
273.2 47.436 L
285.8 40.276 L
298.4 33.116 L
311 25.956 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.929 0.694 0.125 RC
1 LJ
0.833 LW
N
59 161.2 M
71.6 158.575 L
84.2 155.472 L
96.8 151.892 L
109.4 147.835 L
122 143.3 L
134.6 138.288 L
147.2 132.799 L
159.8 126.832 L
172.4 120.388 L
185 113.467 L
197.6 106.068 L
210.2 98.192 L
222.8 89.839 L
235.4 81.008 L
248 71.7 L
260.6 61.915 L
273.2 51.652 L
285.8 40.912 L
298.4 29.695 L
311 18 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
1 GC
N
203 79 M
203 22 L
63 22 L
63 79 L
cp
f
GR
GS
[0.60175 0 0 0.60248 73.32621 20.05288] CT
/Helvetica 13.5 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(m\345linger) t 
GR
GR
GS
[0.60175 0 0 0.60248 55.99497 20.05288] CT
0 0.447 0.741 RC
N
f1395082454
f
GR
GS
[0.60175 0 0 0.60248 73.32621 30.6252] CT
/Helvetica 13.5 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(tilpassning 1) t 
GR
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
1 0 0 RC
[10 6] 0 setdash
2 LJ
0.833 LW
N
68.009 50.5 M
118.098 50.5 L
S
GR
GS
[0.60175 0 0 0.60248 73.32621 41.19753] CT
/Helvetica 13.5 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(tilpassning 2) t 
GR
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.929 0.694 0.125 RC
1 LJ
0.833 LW
N
68.009 68.048 M
118.098 68.048 L
S
GR
GS
[0.60175 0 0 0.60248 0 0.2] CT
0.149 GC
10.0 ML
0.833 LW
N
63 79 M
63 22 L
203 22 L
203 79 L
cp
S
GR
%%Trailer
%%Pages: 1
%%EOF
