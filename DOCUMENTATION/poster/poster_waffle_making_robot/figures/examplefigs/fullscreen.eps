%!PS-Adobe-3.0 EPSF-3.0
%%Creator: (MATLAB, The Mathworks, Inc. Version 9.9.0.1570001 \(R2020b\) Update 4. Operating System: Windows 10)
%%Title: (C:/Users/<USER>/OneDrive - Universitetet i Agder/Jobb/UiA/Fag/EVU/MATLAB/fullscreen.eps)
%%CreationDate: 2021-02-08T19:11:21
%%Pages: (atend)
%%BoundingBox:     0     0  1440   814
%%LanguageLevel: 3
%%EndComments
%%BeginProlog
%%BeginResource: procset (Apache XML Graphics Std ProcSet) 1.2 0
%%Version: 1.2 0
%%Copyright: (Copyright 2001-2003,2010 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/bd{bind def}bind def
/ld{load def}bd
/GR/grestore ld
/GS/gsave ld
/RM/rmoveto ld
/C/curveto ld
/t/show ld
/L/lineto ld
/ML/setmiterlimit ld
/CT/concat ld
/f/fill ld
/N/newpath ld
/S/stroke ld
/CC/setcmykcolor ld
/A/ashow ld
/cp/closepath ld
/RC/setrgbcolor ld
/LJ/setlinejoin ld
/GC/setgray ld
/LW/setlinewidth ld
/M/moveto ld
/re {4 2 roll M
1 index 0 rlineto
0 exch rlineto
neg 0 rlineto
cp } bd
/_ctm matrix def
/_tm matrix def
/BT { _ctm currentmatrix pop matrix _tm copy pop 0 0 moveto } bd
/ET { _ctm setmatrix } bd
/iTm { _ctm setmatrix _tm concat } bd
/Tm { _tm astore pop iTm 0 0 moveto } bd
/ux 0.0 def
/uy 0.0 def
/F {
  /Tp exch def
  /Tf exch def
  Tf findfont Tp scalefont setfont
  /cf Tf def  /cs Tp def
} bd
/ULS {currentpoint /uy exch def /ux exch def} bd
/ULE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add moveto  Tcx uy To add lineto
  Tt setlinewidth stroke
  grestore
} bd
/OLE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs add moveto Tcx uy To add cs add lineto
  Tt setlinewidth stroke
  grestore
} bd
/SOE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs 10 mul 26 idiv add moveto Tcx uy To add cs 10 mul 26 idiv add lineto
  Tt setlinewidth stroke
  grestore
} bd
/QT {
/Y22 exch store
/X22 exch store
/Y21 exch store
/X21 exch store
currentpoint
/Y21 load 2 mul add 3 div exch
/X21 load 2 mul add 3 div exch
/X21 load 2 mul /X22 load add 3 div
/Y21 load 2 mul /Y22 load add 3 div
/X22 load /Y22 load curveto
} bd
/SSPD {
dup length /d exch dict def
{
/v exch def
/k exch def
currentpagedevice k known {
/cpdv currentpagedevice k get def
v cpdv ne {
/upd false def
/nullv v type /nulltype eq def
/nullcpdv cpdv type /nulltype eq def
nullv nullcpdv or
{
/upd true def
} {
/sametype v type cpdv type eq def
sametype {
v type /arraytype eq {
/vlen v length def
/cpdvlen cpdv length def
vlen cpdvlen eq {
0 1 vlen 1 sub {
/i exch def
/obj v i get def
/cpdobj cpdv i get def
obj cpdobj ne {
/upd true def
exit
} if
} for
} {
/upd true def
} ifelse
} {
v type /dicttype eq {
v {
/dv exch def
/dk exch def
/cpddv cpdv dk get def
dv cpddv ne {
/upd true def
exit
} if
} forall
} {
/upd true def
} ifelse
} ifelse
} if
} ifelse
upd true eq {
d k v put
} if
} if
} if
} forall
d length 0 gt {
d setpagedevice
} if
} bd
/RE { % /NewFontName [NewEncodingArray] /FontName RE -
  findfont dup length dict begin
  {
    1 index /FID ne
    {def} {pop pop} ifelse
  } forall
  /Encoding exch def
  /FontName 1 index def
  currentdict definefont pop
  end
} bind def
%%EndResource
%%BeginResource: procset (Apache XML Graphics EPS ProcSet) 1.0 0
%%Version: 1.0 0
%%Copyright: (Copyright 2002-2003 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/BeginEPSF { %def
/b4_Inc_state save def         % Save state for cleanup
/dict_count countdictstack def % Count objects on dict stack
/op_count count 1 sub def      % Count objects on operand stack
userdict begin                 % Push userdict on dict stack
/showpage { } def              % Redefine showpage, { } = null proc
0 setgray 0 setlinecap         % Prepare graphics state
1 setlinewidth 0 setlinejoin
10 setmiterlimit [ ] 0 setdash newpath
/languagelevel where           % If level not equal to 1 then
{pop languagelevel             % set strokeadjust and
1 ne                           % overprint to their defaults.
{false setstrokeadjust false setoverprint
} if
} if
} bd
/EndEPSF { %def
count op_count sub {pop} repeat            % Clean up stacks
countdictstack dict_count sub {end} repeat
b4_Inc_state restore
} bd
%%EndResource
%FOPBeginFontDict
%%IncludeResource: font Courier-Oblique
%%IncludeResource: font Courier-BoldOblique
%%IncludeResource: font Courier-Bold
%%IncludeResource: font ZapfDingbats
%%IncludeResource: font Symbol
%%IncludeResource: font Helvetica
%%IncludeResource: font Helvetica-Oblique
%%IncludeResource: font Helvetica-Bold
%%IncludeResource: font Helvetica-BoldOblique
%%IncludeResource: font Times-Roman
%%IncludeResource: font Times-Italic
%%IncludeResource: font Times-Bold
%%IncludeResource: font Times-BoldItalic
%%IncludeResource: font Courier
%FOPEndFontDict
%%BeginResource: encoding WinAnsiEncoding
/WinAnsiEncoding [
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /space /exclam /quotedbl
/numbersign /dollar /percent /ampersand /quotesingle
/parenleft /parenright /asterisk /plus /comma
/hyphen /period /slash /zero /one
/two /three /four /five /six
/seven /eight /nine /colon /semicolon
/less /equal /greater /question /at
/A /B /C /D /E
/F /G /H /I /J
/K /L /M /N /O
/P /Q /R /S /T
/U /V /W /X /Y
/Z /bracketleft /backslash /bracketright /asciicircum
/underscore /quoteleft /a /b /c
/d /e /f /g /h
/i /j /k /l /m
/n /o /p /q /r
/s /t /u /v /w
/x /y /z /braceleft /bar
/braceright /asciitilde /bullet /Euro /bullet
/quotesinglbase /florin /quotedblbase /ellipsis /dagger
/daggerdbl /circumflex /perthousand /Scaron /guilsinglleft
/OE /bullet /Zcaron /bullet /bullet
/quoteleft /quoteright /quotedblleft /quotedblright /bullet
/endash /emdash /asciitilde /trademark /scaron
/guilsinglright /oe /bullet /zcaron /Ydieresis
/space /exclamdown /cent /sterling /currency
/yen /brokenbar /section /dieresis /copyright
/ordfeminine /guillemotleft /logicalnot /sfthyphen /registered
/macron /degree /plusminus /twosuperior /threesuperior
/acute /mu /paragraph /middot /cedilla
/onesuperior /ordmasculine /guillemotright /onequarter /onehalf
/threequarters /questiondown /Agrave /Aacute /Acircumflex
/Atilde /Adieresis /Aring /AE /Ccedilla
/Egrave /Eacute /Ecircumflex /Edieresis /Igrave
/Iacute /Icircumflex /Idieresis /Eth /Ntilde
/Ograve /Oacute /Ocircumflex /Otilde /Odieresis
/multiply /Oslash /Ugrave /Uacute /Ucircumflex
/Udieresis /Yacute /Thorn /germandbls /agrave
/aacute /acircumflex /atilde /adieresis /aring
/ae /ccedilla /egrave /eacute /ecircumflex
/edieresis /igrave /iacute /icircumflex /idieresis
/eth /ntilde /ograve /oacute /ocircumflex
/otilde /odieresis /divide /oslash /ugrave
/uacute /ucircumflex /udieresis /yacute /thorn
/ydieresis
] def
%%EndResource
%FOPBeginFontReencode
/Courier-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Oblique exch definefont pop
/Courier-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-BoldOblique exch definefont pop
/Courier-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Bold exch definefont pop
/Helvetica findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica exch definefont pop
/Helvetica-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Oblique exch definefont pop
/Helvetica-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Bold exch definefont pop
/Helvetica-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-BoldOblique exch definefont pop
/Times-Roman findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Roman exch definefont pop
/Times-Italic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Italic exch definefont pop
/Times-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Bold exch definefont pop
/Times-BoldItalic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-BoldItalic exch definefont pop
/Courier findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier exch definefont pop
%FOPEndFontReencode
%%EndProlog
%%Page: 1 1
%%PageBoundingBox: 0 0 1440 814
%%BeginPageSetup
[1 0 0 -1 0 814] CT
%%EndPageSetup
GS
[0.6 0 0 0.6 0 0.39996] CT
1 GC
N
0 0 2400 1356 re
f
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
1 GC
N
0 0 2400 1356 re
f
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
1 GC
N
312 1207 M
2172 1207 L
2172 102 L
312 102 L
cp
f
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 1207 M
2172 1207 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 102 M
2172 102 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 1207 M
312 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
498 1207 M
498 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
684 1207 M
684 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
870 1207 M
870 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1056 1207 M
1056 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1242 1207 M
1242 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1428 1207 M
1428 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1614 1207 M
1614 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1800 1207 M
1800 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1986 1207 M
1986 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 1207 M
2172 1188.4 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 102 M
312 120.6 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
498 102 M
498 120.6 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
684 102 M
684 120.6 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
870 102 M
870 120.6 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1056 102 M
1056 120.6 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1242 102 M
1242 120.6 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1428 102 M
1428 120.6 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1614 102 M
1614 120.6 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1800 102 M
1800 120.6 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
1986 102 M
1986 120.6 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 102 M
2172 120.6 L
S
GR
GS
[0.6 0 0 0.6 187.2 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-5 17 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.6 0 0 0.6 298.8 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(0.2) t 
GR
GR
GS
[0.6 0 0 0.6 410.4 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(0.4) t 
GR
GR
GS
[0.6 0 0 0.6 522 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(0.6) t 
GR
GR
GS
[0.6 0 0 0.6 633.6 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(0.8) t 
GR
GR
GS
[0.6 0 0 0.6 745.2 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-5 17 moveto 
1 -1 scale
(1) t 
GR
GR
GS
[0.6 0 0 0.6 856.8 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(1.2) t 
GR
GR
GS
[0.6 0 0 0.6 968.4 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(1.4) t 
GR
GR
GS
[0.6 0 0 0.6 1080 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(1.6) t 
GR
GR
GS
[0.6 0 0 0.6 1191.6 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(1.8) t 
GR
GR
GS
[0.6 0 0 0.6 1303.2 728.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-5 17 moveto 
1 -1 scale
(2) t 
GR
GR
GS
[0.6 0 0 0.6 745.20051 742.20002] CT
0.149 GC
/Helvetica 18.333 F
GS
[1 0 0 1 0 0] CT
-27 19 moveto 
1 -1 scale
(x-akse) t 
GR
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 1207 M
312 102 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 1207 M
2172 102 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 1207 M
330.6 1207 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 1068.875 M
330.6 1068.875 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 930.75 M
330.6 930.75 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 792.625 M
330.6 792.625 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 654.5 M
330.6 654.5 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 516.375 M
330.6 516.375 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 378.25 M
330.6 378.25 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 240.125 M
330.6 240.125 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
312 102 M
330.6 102 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 1207 M
2153.4 1207 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 1068.875 M
2153.4 1068.875 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 930.75 M
2153.4 930.75 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 792.625 M
2153.4 792.625 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 654.5 M
2153.4 654.5 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 516.375 M
2153.4 516.375 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 378.25 M
2153.4 378.25 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 240.125 M
2153.4 240.125 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
2172 102 M
2153.4 102 L
S
GR
GS
[0.6 0 0 0.6 183.20001 724.6] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.06) t 
GR
GR
GS
[0.6 0 0 0.6 183.20001 641.72499] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.08) t 
GR
GR
GS
[0.6 0 0 0.6 183.20001 558.84999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-24 6.5 moveto 
1 -1 scale
(0.1) t 
GR
GR
GS
[0.6 0 0 0.6 183.20001 475.97498] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.12) t 
GR
GR
GS
[0.6 0 0 0.6 183.20001 393.09998] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.14) t 
GR
GR
GS
[0.6 0 0 0.6 183.20001 310.22498] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.16) t 
GR
GR
GS
[0.6 0 0 0.6 183.20001 227.34994] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.18) t 
GR
GR
GS
[0.6 0 0 0.6 183.20001 144.47496] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-24 6.5 moveto 
1 -1 scale
(0.2) t 
GR
GR
GS
[0.6 0 0 0.6 183.20001 61.59997] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.22) t 
GR
GR
GS
[0 -0.6 0.6 0 160.40001 393.09969] CT
0.149 GC
/Helvetica 18.333 F
GS
[1 0 0 1 0 0] CT
-27 -5 moveto 
1 -1 scale
(y-akse) t 
GR
GR
GS
[0.6 0 0 0.6 187.2 641.72499] CT
0 0.447 0.741 RC
N
0 -4.583 M
2.531 -4.583 4.583 -2.531 4.583 0 C
4.583 0 L
4.583 2.531 2.531 4.583 0 4.583 C
-2.531 4.583 -4.583 2.531 -4.583 0 C
-4.583 -2.531 -2.531 -4.583 0 -4.583 C
cp
0 -5.417 M
-2.992 -5.417 -5.417 -2.992 -5.417 0 C
-5.417 2.992 -2.992 5.417 0 5.417 C
2.992 5.417 5.417 2.992 5.417 0 C
5.417 0 L
5.417 -2.992 2.992 -5.417 0 -5.417 C
cp
f
GR
GS
[0.6 0 0 0.6 745.2 475.97498] CT
0 0.447 0.741 RC
N
/f1395082454{0 -4.583 M
2.531 -4.583 4.583 -2.531 4.583 0 C
4.583 0 L
4.583 2.531 2.531 4.583 0 4.583 C
-2.531 4.583 -4.583 2.531 -4.583 0 C
-4.583 -2.531 -2.531 -4.583 0 -4.583 C
cp
0 -5.417 M
-2.992 -5.417 -5.417 -2.992 -5.417 0 C
-5.417 2.992 -2.992 5.417 0 5.417 C
2.992 5.417 5.417 2.992 5.417 0 C
5.417 0 L
5.417 -2.992 2.992 -5.417 0 -5.417 C
cp}def
f1395082454
f
GR
GS
[0.6 0 0 0.6 1303.2 144.47496] CT
0 0.447 0.741 RC
N
f1395082454
f
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
1 0 0 RC
[10 6] 0 setdash
2 LJ
0.833 LW
N
312 1114.917 M
405 1073.479 L
498 1032.042 L
591 990.604 L
684 949.167 L
777 907.729 L
870 866.292 L
963 824.854 L
1056 783.417 L
1149 741.979 L
1242 700.542 L
1335 659.104 L
1428 617.667 L
1521 576.229 L
1614 534.792 L
1707 493.354 L
1800 451.917 L
1893 410.479 L
1986 369.042 L
2079 327.604 L
2172 286.167 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.929 0.694 0.125 RC
1 LJ
0.833 LW
N
312 1068.875 M
405 1053.681 L
498 1035.725 L
591 1015.006 L
684 991.525 L
777 965.281 L
870 936.275 L
963 904.506 L
1056 869.975 L
1149 832.681 L
1242 792.625 L
1335 749.806 L
1428 704.225 L
1521 655.881 L
1614 604.775 L
1707 550.906 L
1800 494.275 L
1893 434.881 L
1986 372.725 L
2079 307.806 L
2172 240.125 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
1 GC
N
2159 182 M
2159 115 L
2011 115 L
2011 182 L
cp
f
GR
GS
[0.6 0 0 0.6 1241.90962 76.98487] CT
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
0 6 moveto 
1 -1 scale
(m\345linger) t 
GR
GR
GS
[0.6 0 0 0.6 1224.63047 76.98487] CT
0 0.447 0.741 RC
N
f1395082454
f
GR
GS
[0.6 0 0 0.6 1241.90962 89.49997] CT
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
0 6 moveto 
1 -1 scale
(tilpassning 1) t 
GR
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
1 0 0 RC
[10 6] 0 setdash
2 LJ
0.833 LW
N
2016.008 148.5 M
2066.093 148.5 L
S
GR
GS
[0.6 0 0 0.6 1241.90962 102.01506] CT
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
0 6 moveto 
1 -1 scale
(tilpassning 2) t 
GR
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.929 0.694 0.125 RC
1 LJ
0.833 LW
N
2016.008 169.358 M
2066.093 169.358 L
S
GR
GS
[0.6 0 0 0.6 0 0.39996] CT
0.149 GC
10.0 ML
0.833 LW
N
2011 182 M
2011 115 L
2159 115 L
2159 182 L
cp
S
GR
%%Trailer
%%Pages: 1
%%EOF
