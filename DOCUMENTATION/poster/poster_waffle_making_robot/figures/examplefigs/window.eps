%!PS-Adobe-3.0 EPSF-3.0
%%Creator: (MATLAB, The Mathworks, Inc. Version 9.9.0.1570001 \(R2020b\) Update 4. Operating System: Windows 10)
%%Title: (C:/Users/<USER>/OneDrive - Universitetet i Agder/Jobb/UiA/Fag/EVU/MATLAB/window.eps)
%%CreationDate: 2021-02-08T19:12:13
%%Pages: (atend)
%%BoundingBox:     0     0   420   315
%%LanguageLevel: 3
%%EndComments
%%BeginProlog
%%BeginResource: procset (Apache XML Graphics Std ProcSet) 1.2 0
%%Version: 1.2 0
%%Copyright: (Copyright 2001-2003,2010 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/bd{bind def}bind def
/ld{load def}bd
/GR/grestore ld
/GS/gsave ld
/RM/rmoveto ld
/C/curveto ld
/t/show ld
/L/lineto ld
/ML/setmiterlimit ld
/CT/concat ld
/f/fill ld
/N/newpath ld
/S/stroke ld
/CC/setcmykcolor ld
/A/ashow ld
/cp/closepath ld
/RC/setrgbcolor ld
/LJ/setlinejoin ld
/GC/setgray ld
/LW/setlinewidth ld
/M/moveto ld
/re {4 2 roll M
1 index 0 rlineto
0 exch rlineto
neg 0 rlineto
cp } bd
/_ctm matrix def
/_tm matrix def
/BT { _ctm currentmatrix pop matrix _tm copy pop 0 0 moveto } bd
/ET { _ctm setmatrix } bd
/iTm { _ctm setmatrix _tm concat } bd
/Tm { _tm astore pop iTm 0 0 moveto } bd
/ux 0.0 def
/uy 0.0 def
/F {
  /Tp exch def
  /Tf exch def
  Tf findfont Tp scalefont setfont
  /cf Tf def  /cs Tp def
} bd
/ULS {currentpoint /uy exch def /ux exch def} bd
/ULE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add moveto  Tcx uy To add lineto
  Tt setlinewidth stroke
  grestore
} bd
/OLE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs add moveto Tcx uy To add cs add lineto
  Tt setlinewidth stroke
  grestore
} bd
/SOE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs 10 mul 26 idiv add moveto Tcx uy To add cs 10 mul 26 idiv add lineto
  Tt setlinewidth stroke
  grestore
} bd
/QT {
/Y22 exch store
/X22 exch store
/Y21 exch store
/X21 exch store
currentpoint
/Y21 load 2 mul add 3 div exch
/X21 load 2 mul add 3 div exch
/X21 load 2 mul /X22 load add 3 div
/Y21 load 2 mul /Y22 load add 3 div
/X22 load /Y22 load curveto
} bd
/SSPD {
dup length /d exch dict def
{
/v exch def
/k exch def
currentpagedevice k known {
/cpdv currentpagedevice k get def
v cpdv ne {
/upd false def
/nullv v type /nulltype eq def
/nullcpdv cpdv type /nulltype eq def
nullv nullcpdv or
{
/upd true def
} {
/sametype v type cpdv type eq def
sametype {
v type /arraytype eq {
/vlen v length def
/cpdvlen cpdv length def
vlen cpdvlen eq {
0 1 vlen 1 sub {
/i exch def
/obj v i get def
/cpdobj cpdv i get def
obj cpdobj ne {
/upd true def
exit
} if
} for
} {
/upd true def
} ifelse
} {
v type /dicttype eq {
v {
/dv exch def
/dk exch def
/cpddv cpdv dk get def
dv cpddv ne {
/upd true def
exit
} if
} forall
} {
/upd true def
} ifelse
} ifelse
} if
} ifelse
upd true eq {
d k v put
} if
} if
} if
} forall
d length 0 gt {
d setpagedevice
} if
} bd
/RE { % /NewFontName [NewEncodingArray] /FontName RE -
  findfont dup length dict begin
  {
    1 index /FID ne
    {def} {pop pop} ifelse
  } forall
  /Encoding exch def
  /FontName 1 index def
  currentdict definefont pop
  end
} bind def
%%EndResource
%%BeginResource: procset (Apache XML Graphics EPS ProcSet) 1.0 0
%%Version: 1.0 0
%%Copyright: (Copyright 2002-2003 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/BeginEPSF { %def
/b4_Inc_state save def         % Save state for cleanup
/dict_count countdictstack def % Count objects on dict stack
/op_count count 1 sub def      % Count objects on operand stack
userdict begin                 % Push userdict on dict stack
/showpage { } def              % Redefine showpage, { } = null proc
0 setgray 0 setlinecap         % Prepare graphics state
1 setlinewidth 0 setlinejoin
10 setmiterlimit [ ] 0 setdash newpath
/languagelevel where           % If level not equal to 1 then
{pop languagelevel             % set strokeadjust and
1 ne                           % overprint to their defaults.
{false setstrokeadjust false setoverprint
} if
} if
} bd
/EndEPSF { %def
count op_count sub {pop} repeat            % Clean up stacks
countdictstack dict_count sub {end} repeat
b4_Inc_state restore
} bd
%%EndResource
%FOPBeginFontDict
%%IncludeResource: font Courier-Oblique
%%IncludeResource: font Courier-BoldOblique
%%IncludeResource: font Courier-Bold
%%IncludeResource: font ZapfDingbats
%%IncludeResource: font Symbol
%%IncludeResource: font Helvetica
%%IncludeResource: font Helvetica-Oblique
%%IncludeResource: font Helvetica-Bold
%%IncludeResource: font Helvetica-BoldOblique
%%IncludeResource: font Times-Roman
%%IncludeResource: font Times-Italic
%%IncludeResource: font Times-Bold
%%IncludeResource: font Times-BoldItalic
%%IncludeResource: font Courier
%FOPEndFontDict
%%BeginResource: encoding WinAnsiEncoding
/WinAnsiEncoding [
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /space /exclam /quotedbl
/numbersign /dollar /percent /ampersand /quotesingle
/parenleft /parenright /asterisk /plus /comma
/hyphen /period /slash /zero /one
/two /three /four /five /six
/seven /eight /nine /colon /semicolon
/less /equal /greater /question /at
/A /B /C /D /E
/F /G /H /I /J
/K /L /M /N /O
/P /Q /R /S /T
/U /V /W /X /Y
/Z /bracketleft /backslash /bracketright /asciicircum
/underscore /quoteleft /a /b /c
/d /e /f /g /h
/i /j /k /l /m
/n /o /p /q /r
/s /t /u /v /w
/x /y /z /braceleft /bar
/braceright /asciitilde /bullet /Euro /bullet
/quotesinglbase /florin /quotedblbase /ellipsis /dagger
/daggerdbl /circumflex /perthousand /Scaron /guilsinglleft
/OE /bullet /Zcaron /bullet /bullet
/quoteleft /quoteright /quotedblleft /quotedblright /bullet
/endash /emdash /asciitilde /trademark /scaron
/guilsinglright /oe /bullet /zcaron /Ydieresis
/space /exclamdown /cent /sterling /currency
/yen /brokenbar /section /dieresis /copyright
/ordfeminine /guillemotleft /logicalnot /sfthyphen /registered
/macron /degree /plusminus /twosuperior /threesuperior
/acute /mu /paragraph /middot /cedilla
/onesuperior /ordmasculine /guillemotright /onequarter /onehalf
/threequarters /questiondown /Agrave /Aacute /Acircumflex
/Atilde /Adieresis /Aring /AE /Ccedilla
/Egrave /Eacute /Ecircumflex /Edieresis /Igrave
/Iacute /Icircumflex /Idieresis /Eth /Ntilde
/Ograve /Oacute /Ocircumflex /Otilde /Odieresis
/multiply /Oslash /Ugrave /Uacute /Ucircumflex
/Udieresis /Yacute /Thorn /germandbls /agrave
/aacute /acircumflex /atilde /adieresis /aring
/ae /ccedilla /egrave /eacute /ecircumflex
/edieresis /igrave /iacute /icircumflex /idieresis
/eth /ntilde /ograve /oacute /ocircumflex
/otilde /odieresis /divide /oslash /ugrave
/uacute /ucircumflex /udieresis /yacute /thorn
/ydieresis
] def
%%EndResource
%FOPBeginFontReencode
/Courier-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Oblique exch definefont pop
/Courier-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-BoldOblique exch definefont pop
/Courier-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Bold exch definefont pop
/Helvetica findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica exch definefont pop
/Helvetica-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Oblique exch definefont pop
/Helvetica-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Bold exch definefont pop
/Helvetica-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-BoldOblique exch definefont pop
/Times-Roman findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Roman exch definefont pop
/Times-Italic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Italic exch definefont pop
/Times-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Bold exch definefont pop
/Times-BoldItalic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-BoldItalic exch definefont pop
/Courier findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier exch definefont pop
%FOPEndFontReencode
%%EndProlog
%%Page: 1 1
%%PageBoundingBox: 0 0 420 315
%%BeginPageSetup
[1 0 0 -1 0 315] CT
%%EndPageSetup
GS
[0.6 0 0 0.6 0 0] CT
1 GC
N
0 0 700 525 re
f
GR
GS
[0.6 0 0 0.6 0 0] CT
1 GC
N
0 0 700 525 re
f
GR
GS
[0.6 0 0 0.6 0 0] CT
1 GC
N
91 467 M
634 467 L
634 39 L
91 39 L
cp
f
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 467 M
634 467 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 39 M
634 39 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 467 M
91 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
145.3 467 M
145.3 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
199.6 467 M
199.6 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
253.9 467 M
253.9 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
308.2 467 M
308.2 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
362.5 467 M
362.5 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
416.8 467 M
416.8 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
471.1 467 M
471.1 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
525.4 467 M
525.4 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
579.7 467 M
579.7 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 467 M
634 461.57 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 39 M
91 44.43 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
145.3 39 M
145.3 44.43 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
199.6 39 M
199.6 44.43 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
253.9 39 M
253.9 44.43 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
308.2 39 M
308.2 44.43 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
362.5 39 M
362.5 44.43 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
416.8 39 M
416.8 44.43 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
471.1 39 M
471.1 44.43 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
525.4 39 M
525.4 44.43 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
579.7 39 M
579.7 44.43 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 39 M
634 44.43 L
S
GR
GS
[0.6 0 0 0.6 54.6 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-5 17 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.6 0 0 0.6 87.18 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(0.2) t 
GR
GR
GS
[0.6 0 0 0.6 119.76 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(0.4) t 
GR
GR
GS
[0.6 0 0 0.6 152.34001 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(0.6) t 
GR
GR
GS
[0.6 0 0 0.6 184.92001 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(0.8) t 
GR
GR
GS
[0.6 0 0 0.6 217.5 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-5 17 moveto 
1 -1 scale
(1) t 
GR
GR
GS
[0.6 0 0 0.6 250.08001 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(1.2) t 
GR
GR
GS
[0.6 0 0 0.6 282.66 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(1.4) t 
GR
GR
GS
[0.6 0 0 0.6 315.24001 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(1.6) t 
GR
GR
GS
[0.6 0 0 0.6 347.82001 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-12 17 moveto 
1 -1 scale
(1.8) t 
GR
GR
GS
[0.6 0 0 0.6 380.4 284.19999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-5 17 moveto 
1 -1 scale
(2) t 
GR
GR
GS
[0.6 0 0 0.6 217.50015 297.80001] CT
0.149 GC
/Helvetica 18.333 F
GS
[1 0 0 1 0 0] CT
-27 19 moveto 
1 -1 scale
(x-akse) t 
GR
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 467 M
91 39 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 467 M
634 39 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 467 M
96.43 467 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 413.5 M
96.43 413.5 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 360 M
96.43 360 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 306.5 M
96.43 306.5 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 253 M
96.43 253 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 199.5 M
96.43 199.5 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 146 M
96.43 146 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 92.5 M
96.43 92.5 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
91 39 M
96.43 39 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 467 M
628.57 467 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 413.5 M
628.57 413.5 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 360 M
628.57 360 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 306.5 M
628.57 306.5 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 253 M
628.57 253 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 199.5 M
628.57 199.5 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 146 M
628.57 146 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 92.5 M
628.57 92.5 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.833 LW
N
634 39 M
628.57 39 L
S
GR
GS
[0.6 0 0 0.6 50.6 280.2] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.06) t 
GR
GR
GS
[0.6 0 0 0.6 50.6 248.1] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.08) t 
GR
GR
GS
[0.6 0 0 0.6 50.6 216] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-24 6.5 moveto 
1 -1 scale
(0.1) t 
GR
GR
GS
[0.6 0 0 0.6 50.6 183.9] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.12) t 
GR
GR
GS
[0.6 0 0 0.6 50.6 151.8] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.14) t 
GR
GR
GS
[0.6 0 0 0.6 50.6 119.70001] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.16) t 
GR
GR
GS
[0.6 0 0 0.6 50.6 87.59999] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.18) t 
GR
GR
GS
[0.6 0 0 0.6 50.6 55.5] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-24 6.5 moveto 
1 -1 scale
(0.2) t 
GR
GR
GS
[0.6 0 0 0.6 50.6 23.4] CT
0.149 GC
/Helvetica 16.667 F
GS
[1 0 0 1 0 0] CT
-33 6.5 moveto 
1 -1 scale
(0.22) t 
GR
GR
GS
[0 -0.6 0.6 0 27.8 151.79988] CT
0.149 GC
/Helvetica 18.333 F
GS
[1 0 0 1 0 0] CT
-27 -5 moveto 
1 -1 scale
(y-akse) t 
GR
GR
GS
[0.6 0 0 0.6 54.6 248.1] CT
0 0.447 0.741 RC
N
0 -4.583 M
2.531 -4.583 4.583 -2.531 4.583 0 C
4.583 0 L
4.583 2.531 2.531 4.583 0 4.583 C
-2.531 4.583 -4.583 2.531 -4.583 0 C
-4.583 -2.531 -2.531 -4.583 0 -4.583 C
cp
0 -5.417 M
-2.992 -5.417 -5.417 -2.992 -5.417 0 C
-5.417 2.992 -2.992 5.417 0 5.417 C
2.992 5.417 5.417 2.992 5.417 0 C
5.417 0 L
5.417 -2.992 2.992 -5.417 0 -5.417 C
cp
f
GR
GS
[0.6 0 0 0.6 217.5 183.9] CT
0 0.447 0.741 RC
N
/f1395082454{0 -4.583 M
2.531 -4.583 4.583 -2.531 4.583 0 C
4.583 0 L
4.583 2.531 2.531 4.583 0 4.583 C
-2.531 4.583 -4.583 2.531 -4.583 0 C
-4.583 -2.531 -2.531 -4.583 0 -4.583 C
cp
0 -5.417 M
-2.992 -5.417 -5.417 -2.992 -5.417 0 C
-5.417 2.992 -2.992 5.417 0 5.417 C
2.992 5.417 5.417 2.992 5.417 0 C
5.417 0 L
5.417 -2.992 2.992 -5.417 0 -5.417 C
cp}def
f1395082454
f
GR
GS
[0.6 0 0 0.6 380.4 55.5] CT
0 0.447 0.741 RC
N
f1395082454
f
GR
GS
[0.6 0 0 0.6 0 0] CT
1 0 0 RC
[10 6] 0 setdash
2 LJ
0.833 LW
N
91 431.333 M
118.15 415.283 L
145.3 399.233 L
172.45 383.183 L
199.6 367.133 L
226.75 351.083 L
253.9 335.033 L
281.05 318.983 L
308.2 302.933 L
335.35 286.883 L
362.5 270.833 L
389.65 254.783 L
416.8 238.733 L
443.95 222.683 L
471.1 206.633 L
498.25 190.583 L
525.4 174.533 L
552.55 158.483 L
579.7 142.433 L
606.85 126.383 L
634 110.333 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.929 0.694 0.125 RC
1 LJ
0.833 LW
N
91 413.5 M
118.15 407.615 L
145.3 400.66 L
172.45 392.635 L
199.6 383.54 L
226.75 373.375 L
253.9 362.14 L
281.05 349.835 L
308.2 336.46 L
335.35 322.015 L
362.5 306.5 L
389.65 289.915 L
416.8 272.26 L
443.95 253.535 L
471.1 233.74 L
498.25 212.875 L
525.4 190.94 L
552.55 167.935 L
579.7 143.86 L
606.85 118.715 L
634 92.5 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
1 GC
N
621 119 M
621 52 L
473 52 L
473 119 L
cp
f
GR
GS
[0.6 0 0 0.6 319.10966 38.78491] CT
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
0 6 moveto 
1 -1 scale
(m\345linger) t 
GR
GR
GS
[0.6 0 0 0.6 301.83045 38.78491] CT
0 0.447 0.741 RC
N
f1395082454
f
GR
GS
[0.6 0 0 0.6 319.10966 51.3] CT
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
0 6 moveto 
1 -1 scale
(tilpassning 1) t 
GR
GR
GS
[0.6 0 0 0.6 0 0] CT
1 0 0 RC
[10 6] 0 setdash
2 LJ
0.833 LW
N
478.008 85.5 M
528.093 85.5 L
S
GR
GS
[0.6 0 0 0.6 319.10966 63.81509] CT
/Helvetica 15 F
GS
[1 0 0 1 0 0] CT
0 6 moveto 
1 -1 scale
(tilpassning 2) t 
GR
GR
GS
[0.6 0 0 0.6 0 0] CT
0.929 0.694 0.125 RC
1 LJ
0.833 LW
N
478.008 106.358 M
528.093 106.358 L
S
GR
GS
[0.6 0 0 0.6 0 0] CT
0.149 GC
10.0 ML
0.833 LW
N
473 119 M
473 52 L
621 52 L
621 119 L
cp
S
GR
%%Trailer
%%Pages: 1
%%EOF
