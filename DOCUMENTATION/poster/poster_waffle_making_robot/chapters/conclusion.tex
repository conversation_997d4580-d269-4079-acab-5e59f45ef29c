\section{Summary and conclusions}
The waffle making robot performed well at its primary task, but failed at its secondary tasks. 
The system made waffles in a consistent and reliable fashion, and it had an entertainment mode that could be used to attract onlookers. It had two modes, one consisting of predetermined movements and the other using a camera to inform its movements.
The concept of camera assisted movement showed promise, but ultimately failed in execution. %Just like communism. 
Camera detection of markers worked accurately. Robot pose recording did not. 
A servo for operating spray grease was planned, but was never implemented due to electronics issues. 

The project drew attention when tested in a marketing setting. Attention was highest at the start and end of the cooking process, when the robot was actively working on waffle making.
